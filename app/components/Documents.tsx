import Image from 'next/image';
import React, { useState, useEffect } from 'react';
import InformationBox from './InformationBox';
import FileUploadField from './FileUploadField';
import { Switch } from '@/components/ui/switch';
import {AcademicData, Proficiency} from '@/common';
import SectionLayout from './layout/SectionLayout';
import Publication from '@/app/assets/img/publication.png';
import { AcademicFieldProps } from '@/types';
import StudentsDocumentsLayout from './layout/StudentsDocumentsLayout';

// Interface for child information
interface ChildInfo {
    id: string;
    name: string;
    photo: File | null;
    passport: File | null;
}

const Documents = () => {
    const [fields, setFields] = useState<AcademicFieldProps[]>([
        { id: 'ssc', label: 'SSC', files: [] },
        { id: 'hsc', label: 'HSC', files: [] },
        { id: 'bachelor', label: 'Bachelor', files: [] },
        { id: 'masters', label: 'Masters', files: [] },
    ]);

    const [profileFields, setProfileFields] = useState<AcademicFieldProps[]>([
        { id: 'photo', label: 'Photo', files: [] },
        { id: 'passport', label: 'Passport', files: [] },
        { id: 'signature', label: 'Signature', files: [] },
    ]);

    const [proficiencyFields, setproficiencyFields] = useState<AcademicFieldProps[]>([
        { id: 'duolingo', label: 'Duolingo', files: [] },
        { id: 'ielts', label: 'IELTS', files: [] },
    ]);

    const [sponsorFields, setSponsorFields] = useState<AcademicFieldProps[]>([
        { id: 'photo', label: 'Photo', files: [] },
        { id: 'bank_statement', label: 'Bank Statement', files: [] },
    ]);

    // Dependents state
    const [hasDependents, setHasDependents] = useState(false);
    const [childrenInfo, setChildrenInfo] = useState<ChildInfo[]>([]);

    // Functions for handling child information
    const addChildInfo = () => {
        const newChild: ChildInfo = {
            id: `child-${Date.now()}`,
            name: '',
            photo: null,
            passport: null
        };
        setChildrenInfo([...childrenInfo, newChild]);
    };

    const removeChildInfo = (childId: string) => {
        setChildrenInfo(childrenInfo.filter(child => child.id !== childId));
    };

    const updateChildInfo = (childId: string, field: keyof ChildInfo, value: any) => {
        setChildrenInfo(childrenInfo.map(child =>
            child.id === childId ? { ...child, [field]: value } : child
        ));
    };

    const handleFileUpload = (childId: string, field: 'photo' | 'passport', file: File | null) => {
        updateChildInfo(childId, field, file);
    };

    // const tableHead = ['Title', 'Document', 'Actions'];
    // const handleUpload = (index: number) => {
    //     // Handle file upload logic here
    //     // console.log(`Upload clicked for row ${index}`);
    //   };
    
    //   const handleView = (index: number) => {
    //     console.log(`View clicked for row ${index}`);
    //   };
    
    //   const handleEdit = (index: number) => {
    //     console.log(`Edit clicked for row ${index}`);
    //   };
    return (
        <SectionLayout  heading={'Documents Form'}>
            <StudentsDocumentsLayout sectionTitle='Profile'>
                <FileUploadField 
                    fields={profileFields} 
                    setFields={setProfileFields} 
                />
            </StudentsDocumentsLayout>
            <StudentsDocumentsLayout className={'mt-[30px]'}  sectionTitle='Academic'>
                <FileUploadField 
                    fields={fields} 
                    setFields={setFields} 
                />
            </StudentsDocumentsLayout>
            <StudentsDocumentsLayout 
                className={'mt-[30px]'} 
                sectionTitle='Proficiency'
            >
                <FileUploadField 
                    fields={proficiencyFields} 
                    setFields={setproficiencyFields} 
                />
            </StudentsDocumentsLayout>
            <StudentsDocumentsLayout 
                className={'mt-[30px]'} 
                sectionTitle='Sponsor'
            >
                <div className='flex flex-col md:flex-row mb-[18px]'>
                    <label className='w-[15%] block text-base font-medium mb-2 text-grayFive'>Name</label>
                    <span className='mt-0.5 w-[75%] font-semibold text-base leading-5 text-grayFive'>Phillip Carter</span>
                </div>
                <FileUploadField 
                    fields={sponsorFields} 
                    setFields={setSponsorFields} 
                />
            </StudentsDocumentsLayout>
            {/* Dependents Section */}
            <div className='mt-[30px] bg-white rounded-lg border border-gray-200 p-6'>
                {/* Switch Section */}
                <div className='flex items-center gap-3 mb-6'>
                    <span className='font-medium text-base text-grayFive'>Do you want to take dependents or not?</span>
                    <Switch
                        checked={hasDependents}
                        onCheckedChange={setHasDependents}
                        id='dependents-switch'
                    />
                </div>

                {hasDependents && (
                    <div className='space-y-8'>
                        {/* Main Dependents Section */}
                        <div>
                            <div className='flex items-center gap-2 mb-6'>
                                <h3 className='font-semibold text-xl text-graySix'>Dependents</h3>
                                <span className='text-xs text-gray-500'>*Accepted file types: JPEG, PNG, PDF, up to 50 MB</span>
                            </div>

                            {/* Main Dependent Form */}
                            <div className='space-y-6'>
                                {/* Name Field */}
                                <div className='flex flex-col md:flex-row items-start md:items-center gap-3'>
                                    <label className='w-full md:w-[15%] block text-base font-medium text-grayFive'>Name</label>
                                    <span className='w-full md:w-[75%] font-semibold text-base leading-5 text-grayFive bg-gray-50 px-4 py-3 rounded-lg'>John Grey</span>
                                </div>

                                {/* Photo Upload */}
                                <div className='flex flex-col md:flex-row items-start md:items-center gap-3'>
                                    <label className='w-full md:w-[15%] block text-base font-medium text-grayFive'>Photo</label>
                                    <div className='w-full md:w-[75%] flex items-center gap-3 rounded-lg px-5 py-[18px] border border-[#1952BB] border-opacity-20 bg-white'>
                                        <input
                                            type="file"
                                            accept="image/*"
                                            className="hidden"
                                            id="main-photo-upload"
                                        />
                                        <label
                                            htmlFor="main-photo-upload"
                                            className="cursor-pointer bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded text-sm"
                                        >
                                            Choose File
                                        </label>
                                        <span className="text-sm text-gray-500">No File Chosen</span>
                                    </div>
                                </div>

                                {/* Passport Upload */}
                                <div className='flex flex-col md:flex-row items-start md:items-center gap-3'>
                                    <label className='w-full md:w-[15%] block text-base font-medium text-grayFive'>Passport</label>
                                    <div className='w-full md:w-[75%] flex items-center gap-3 rounded-lg px-5 py-[18px] border border-[#1952BB] border-opacity-20 bg-white'>
                                        <input
                                            type="file"
                                            accept=".pdf,image/*"
                                            className="hidden"
                                            id="main-passport-upload"
                                        />
                                        <label
                                            htmlFor="main-passport-upload"
                                            className="cursor-pointer bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded text-sm"
                                        >
                                            Choose File
                                        </label>
                                        <span className="text-sm text-gray-500">No File Chosen</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        {/* Children Section */}
                        {childrenInfo.map((child, index) => (
                            <div key={child.id}>
                                <div className='flex items-center justify-between mb-6'>
                                    <div className='flex items-center gap-2'>
                                        <h3 className='font-semibold text-xl text-graySix'>Child {index + 1}</h3>
                                        <span className='text-xs text-gray-500'>*Accepted file types: JPEG, PNG, PDF, up to 50 MB</span>
                                    </div>
                                    {childrenInfo.length > 0 && (
                                        <button
                                            type="button"
                                            onClick={() => removeChildInfo(child.id)}
                                            className="text-red-500 hover:text-red-700 text-sm font-medium"
                                        >
                                            Remove
                                        </button>
                                    )}
                                </div>

                                <div className='space-y-6'>
                                    {/* Child Name Field */}
                                    <div className='flex flex-col md:flex-row items-start md:items-center gap-3'>
                                        <label className='w-full md:w-[15%] block text-base font-medium text-grayFive'>Name</label>
                                        <input
                                            type="text"
                                            value={child.name}
                                            onChange={(e) => updateChildInfo(child.id, 'name', e.target.value)}
                                            placeholder="Type child name"
                                            className='w-full md:w-[75%] px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500'
                                        />
                                    </div>

                                    {/* Child Photo Upload */}
                                    <div className='flex flex-col md:flex-row items-start md:items-center gap-3'>
                                        <label className='w-full md:w-[15%] block text-base font-medium text-grayFive'>Photo</label>
                                        <div className='w-full md:w-[75%] flex items-center gap-3 rounded-lg px-5 py-[18px] border border-[#1952BB] border-opacity-20 bg-white'>
                                            <input
                                                type="file"
                                                accept="image/*"
                                                onChange={(e) => handleFileUpload(child.id, 'photo', e.target.files?.[0] || null)}
                                                className="hidden"
                                                id={`child-photo-upload-${child.id}`}
                                            />
                                            <label
                                                htmlFor={`child-photo-upload-${child.id}`}
                                                className="cursor-pointer bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded text-sm"
                                            >
                                                Choose File
                                            </label>
                                            <span className="text-sm text-gray-500">
                                                {child.photo ? child.photo.name : 'No File Chosen'}
                                            </span>
                                        </div>
                                    </div>

                                    {/* Child Passport Upload */}
                                    <div className='flex flex-col md:flex-row items-start md:items-center gap-3'>
                                        <label className='w-full md:w-[15%] block text-base font-medium text-grayFive'>Passport</label>
                                        <div className='w-full md:w-[75%] flex items-center gap-3 rounded-lg px-5 py-[18px] border border-[#1952BB] border-opacity-20 bg-white'>
                                            <input
                                                type="file"
                                                accept=".pdf,image/*"
                                                onChange={(e) => handleFileUpload(child.id, 'passport', e.target.files?.[0] || null)}
                                                className="hidden"
                                                id={`child-passport-upload-${child.id}`}
                                            />
                                            <label
                                                htmlFor={`child-passport-upload-${child.id}`}
                                                className="cursor-pointer bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded text-sm"
                                            >
                                                Choose File
                                            </label>
                                            <span className="text-sm text-gray-500">
                                                {child.passport ? child.passport.name : 'No File Chosen'}
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        ))}

                        {/* Add Child Info Button */}
                        <div className='flex justify-start'>
                            <button
                                type="button"
                                onClick={addChildInfo}
                                className="flex items-center gap-2 text-blue-500 hover:text-blue-600 font-medium"
                            >
                                <span className="text-lg">+</span>
                                Add Child Info
                            </button>
                        </div>
                    </div>
                )}
            </div>
            {/* Educations data */}
            <h2 className='font-semibold text-xl text-graySix'>Academic</h2>
            <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
                {AcademicData.map((data, index) => (
                    <InformationBox key={index} data={data} />
                ))}
            </div>
            <h2 className='mt-6 font-semibold text-xl text-graySix'>Proficiency</h2>
            <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
                {Proficiency.map((data, index) => (
                    <InformationBox key={index} data={data} />
                ))}
            </div>

            <h2 className='mt-6 font-semibold text-xl text-graySix'>Publication</h2>
            <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
                <div className='mt-6 border rounded-lg py-4 px-5 border-[#1952BB] border-opacity-20'>
                    <div className='flex justify-between items-center'>
                        <div className='flex flex-col gap-2.5'>
                            <div className='flex items-center gap-4'>
                                <span className='font-medium text-lg text-graySix'>Marine Biology, Climate Change</span>
                                <span className='font-medium text-xs text-primaryColor bg-primaryOne rounded-[16px] py-[2px] px-2'>Feb 12, 2023</span>
                            </div>
                            <p className='font-normal text-xs leading-5 tracking-[0.25px] text-graySix'>International Journal of Business and Management Studies</p>
                        </div>
                        <div>
                            <Image src={Publication} alt="Publications logo" />
                        </div>
                    </div>
                </div>
                <div className='mt-6 border rounded-lg py-4 px-5 border-[#1952BB] border-opacity-20'>
                    <div className='flex justify-between items-center'>
                        <div className='flex flex-col gap-2.5'>
                            <div className='flex items-center gap-4'>
                                <span className='font-medium text-lg text-graySix'>Marine Biology, Climate Change</span>
                                <span className='font-medium text-xs text-primaryColor bg-primaryOne rounded-[16px] py-[2px] px-2'>Feb 12, 2023</span>
                            </div>
                            <p className='font-normal text-xs leading-5 tracking-[0.25px] text-graySix'>International Journal of Business and Management Studies</p>
                        </div>
                        <div>
                            <Image src={Publication} alt="Publications logo" />
                        </div>
                    </div>
                </div>
            </div>
            {/* <Table>
                <TableHeader>
                    <TableRow>
                        {tableHead.map((th, index) => (
                            <TableHead key={index} className='bg-white px-6 py-3.5 font-bold text-xs tracking-[0.4px] text-grayFive'>{th}</TableHead>
                        ))}
                    </TableRow>
                </TableHeader>
                <TableBody>
                    {data.map((item, index) => (
                        <TableRow className='hover:bg-primaryFour' key={index}>
                            <TableCell 
                                className="px-6 py-3.5 font-normal text-xs tracking-[0.4px] text-graySix"
                            >
                                {item.name}
                            </TableCell>
                            <TableCell 
                                className='px-6 py-3.5 flex items-center gap-2.5'
                            >
                                 {item.files && (
                                    <div className="flex flex-wrap gap-2 mt-1">
                                    {item.files.map((file, i) => (
                                        <span
                                        key={i}
                                        className="bg-primaryOne text-[#144296] px-2 py-1 rounded-[50px] text-xs flex items-center gap-1"
                                        >
                                        {file} <button onClick={() => console.log(`Remove ${file}`)}><X_icon className='text-[#144296]' /></button>
                                        </span>
                                    ))}
                                    </div>
                                )}
                            </TableCell>
                            <TableCell 
                                className="px-6 py-3.5 font-normal text-xs tracking-[0.4px] text-graySix"
                            >
                                <div className="flex justify-left gap-2">
                                    <Button onClick={() => handleUpload(index)} size="sm" >
                                        <Edit />
                                    </Button>
                                    <Button onClick={() => handleEdit(index)} size="sm">
                                        <Download />
                                    </Button>
                                    <Button onClick={() => handleUpload(index)} size="sm">
                                        <Upload className='text-primaryColor' />
                                    </Button>
                                    <Button onClick={() => handleView(index)} size="sm">
                                        <Visibility className="text-primaryColor" />
                                    </Button>
                                    </div>
                            </TableCell>
                        </TableRow>
                    ))}
                </TableBody>
            </Table> */}
        </SectionLayout>
    )
}

export default Documents