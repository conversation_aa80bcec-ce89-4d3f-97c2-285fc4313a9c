import Image from 'next/image';
import React, { useState } from 'react';
import Upload from '@/app/assets/svg/upload';
import InformationBox from './InformationBox';
import FileUploadField from './FileUploadField';
import { Button } from '@/components/ui/button';
import { Switch } from '@/components/ui/switch';
import {AcademicData, Proficiency} from '@/common';
import SectionLayout from './layout/SectionLayout';
import Publication from '@/app/assets/img/publication.png';
import { ProficiencyProps, AcademicFieldProps } from '@/types';
import StudentsDocumentsLayout from './layout/StudentsDocumentsLayout';
import InputField from './InputField';
  
  const initialData: ProficiencyProps[] = [
    { name: "IELTS", files: ["IELTS.pdf", "documents1.pdf", "gfds.pdf"] },
    { name: "Duolingo" },
    { name: "<PERSON><PERSON><PERSON>" },
    { name: "<PERSON><PERSON><PERSON>" },
    { name: "SAT/ GRE/ GMAT" },
    { name: "Others" },
];

// Interface for child information
interface ChildInfo {
    id: string;
    name: string;
    photo: File | null;
    passport: File | null;
}

const Documents = () => {
    const [fields, setFields] = useState<AcademicFieldProps[]>([
        { id: 'ssc', label: 'SSC', files: [] },
        { id: 'hsc', label: 'HSC', files: [] },
        { id: 'bachelor', label: 'Bachelor', files: [] },
        { id: 'masters', label: 'Masters', files: [] },
    ]);

    const [profileFields, setProfileFields] = useState<AcademicFieldProps[]>([
        { id: 'photo', label: 'Photo', files: [] },
        { id: 'passport', label: 'Passport', files: [] },
        { id: 'signature', label: 'Signature', files: [] },
    ]);

    const [proficiencyFields, setproficiencyFields] = useState<AcademicFieldProps[]>([
        { id: 'duolingo', label: 'Duolingo', files: [] },
        { id: 'ielts', label: 'IELTS', files: [] },
    ]);

    const [sponsorFields, setSponsorFields] = useState<AcademicFieldProps[]>([
        { id: 'photo', label: 'Photo', files: [] },
        { id: 'bank_statement', label: 'Bank Statement', files: [] },
    ]);

    // Dependents state
    const [hasDependents, setHasDependents] = useState(false);
    const [childrenInfo, setChildrenInfo] = useState<ChildInfo[]>([]);

    // Functions for handling child information
    const addChildInfo = () => {
        const newChild: ChildInfo = {
            id: `child-${Date.now()}`,
            name: '',
            photo: null,
            passport: null
        };
        setChildrenInfo([...childrenInfo, newChild]);
    };

    const removeChildInfo = (childId: string) => {
        setChildrenInfo(childrenInfo.filter(child => child.id !== childId));
    };

    const updateChildInfo = (childId: string, field: keyof ChildInfo, value: any) => {
        setChildrenInfo(childrenInfo.map(child =>
            child.id === childId ? { ...child, [field]: value } : child
        ));
    };

    const handleFileUpload = (childId: string, field: 'photo' | 'passport', file: File | null) => {
        updateChildInfo(childId, field, file);
    };

    // const tableHead = ['Title', 'Document', 'Actions'];
    // const handleUpload = (index: number) => {
    //     // Handle file upload logic here
    //     // console.log(`Upload clicked for row ${index}`);
    //   };
    
    //   const handleView = (index: number) => {
    //     console.log(`View clicked for row ${index}`);
    //   };
    
    //   const handleEdit = (index: number) => {
    //     console.log(`Edit clicked for row ${index}`);
    //   };
    return (
        <SectionLayout  heading={'Documents Form'}>
            <StudentsDocumentsLayout sectionTitle='Profile'>
                <FileUploadField 
                    fields={profileFields} 
                    setFields={setProfileFields} 
                />
            </StudentsDocumentsLayout>
            <StudentsDocumentsLayout className={'mt-[30px]'}  sectionTitle='Academic'>
                <FileUploadField 
                    fields={fields} 
                    setFields={setFields} 
                />
            </StudentsDocumentsLayout>
            <StudentsDocumentsLayout 
                className={'mt-[30px]'} 
                sectionTitle='Proficiency'
            >
                <FileUploadField 
                    fields={proficiencyFields} 
                    setFields={setproficiencyFields} 
                />
            </StudentsDocumentsLayout>
            <StudentsDocumentsLayout 
                className={'mt-[30px]'} 
                sectionTitle='Sponsor'
            >
                <div className='flex flex-col md:flex-row mb-[18px]'>
                    <label className='w-[15%] block text-base font-medium mb-2 text-grayFive'>Name</label>
                    <span className='mt-0.5 w-[75%] font-semibold text-base leading-5 text-grayFive'>Phillip Carter</span>
                </div>
                <FileUploadField 
                    fields={sponsorFields} 
                    setFields={setSponsorFields} 
                />
            </StudentsDocumentsLayout>
            <StudentsDocumentsLayout
                className={'mt-[30px]'}
                sectionTitle='Dependents'
            >
                <div className='flex flex-col md:flex-row gap-3 items-start md:items-center mb-6'>
                    <div className='flex gap-3 items-center'>
                        <Switch
                            checked={hasDependents}
                            onCheckedChange={setHasDependents}
                            id='dependents-switch'
                        />
                        <p className='font-normal text-xs md:text-sm leading-5 text-[#4B5563] text-opacity-70'>
                            Do you have dependents?
                        </p>
                    </div>
                </div>

                {hasDependents && (
                    <div className='space-y-6'>
                        {childrenInfo.map((child, index) => (
                            <div key={child.id} className='border border-gray-200 rounded-lg p-4'>
                                <div className='flex justify-between items-center mb-4'>
                                    <h4 className='font-medium text-lg text-graySix'>Child {index + 1}</h4>
                                    {childrenInfo.length > 1 && (
                                        <button
                                            type="button"
                                            onClick={() => removeChildInfo(child.id)}
                                            className="text-red-500 hover:text-red-700 text-sm"
                                        >
                                            Remove
                                        </button>
                                    )}
                                </div>

                                <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
                                    {/* Name Field */}
                                    <div className='md:col-span-2'>
                                        <InputField
                                            id={`child-name-${child.id}`}
                                            placeholder='Enter child name'
                                            type='text'
                                            label='Name'
                                            value={child.name}
                                            onChange={(e) => updateChildInfo(child.id, 'name', e.target.value)}
                                        />
                                    </div>

                                    {/* Photo Upload */}
                                    <div className='flex flex-col'>
                                        <label className='block text-base font-medium mb-2 text-grayFive'>Photo</label>
                                        <div className='flex items-center gap-3 rounded-lg px-5 py-[18px] border border-[#1952BB] border-opacity-20 bg-white'>
                                            <input
                                                type="file"
                                                accept="image/*"
                                                onChange={(e) => handleFileUpload(child.id, 'photo', e.target.files?.[0] || null)}
                                                className="hidden"
                                                id={`photo-upload-${child.id}`}
                                            />
                                            <label
                                                htmlFor={`photo-upload-${child.id}`}
                                                className="cursor-pointer flex items-center gap-2 text-primaryColor"
                                            >
                                                <Upload className="w-4 h-4" />
                                                Choose Photo
                                            </label>
                                            {child.photo && (
                                                <span className="text-sm text-gray-600">{child.photo.name}</span>
                                            )}
                                        </div>
                                    </div>

                                    {/* Passport Upload */}
                                    <div className='flex flex-col'>
                                        <label className='block text-base font-medium mb-2 text-grayFive'>Passport</label>
                                        <div className='flex items-center gap-3 rounded-lg px-5 py-[18px] border border-[#1952BB] border-opacity-20 bg-white'>
                                            <input
                                                type="file"
                                                accept=".pdf,image/*"
                                                onChange={(e) => handleFileUpload(child.id, 'passport', e.target.files?.[0] || null)}
                                                className="hidden"
                                                id={`passport-upload-${child.id}`}
                                            />
                                            <label
                                                htmlFor={`passport-upload-${child.id}`}
                                                className="cursor-pointer flex items-center gap-2 text-primaryColor"
                                            >
                                                <Upload className="w-4 h-4" />
                                                Choose Passport
                                            </label>
                                            {child.passport && (
                                                <span className="text-sm text-gray-600">{child.passport.name}</span>
                                            )}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        ))}

                        {/* Add Child Info Button */}
                        <div className='flex justify-center mt-6'>
                            <Button
                                type="button"
                                onClick={addChildInfo}
                                className="bg-primaryColor hover:bg-primaryColor/90 text-white px-6 py-2 rounded-lg"
                            >
                                Add Child Info
                            </Button>
                        </div>
                    </div>
                )}
            </StudentsDocumentsLayout>
            {/* Educations data */}
            <h2 className='font-semibold text-xl text-graySix'>Academic</h2>
            <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
                {AcademicData.map((data, index) => (
                    <InformationBox key={index} data={data} />
                ))}
            </div>
            <h2 className='mt-6 font-semibold text-xl text-graySix'>Proficiency</h2>
            <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
                {Proficiency.map((data, index) => (
                    <InformationBox key={index} data={data} />
                ))}
            </div>

            <h2 className='mt-6 font-semibold text-xl text-graySix'>Publication</h2>
            <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
                <div className='mt-6 border rounded-lg py-4 px-5 border-[#1952BB] border-opacity-20'>
                    <div className='flex justify-between items-center'>
                        <div className='flex flex-col gap-2.5'>
                            <div className='flex items-center gap-4'>
                                <span className='font-medium text-lg text-graySix'>Marine Biology, Climate Change</span>
                                <span className='font-medium text-xs text-primaryColor bg-primaryOne rounded-[16px] py-[2px] px-2'>Feb 12, 2023</span>
                            </div>
                            <p className='font-normal text-xs leading-5 tracking-[0.25px] text-graySix'>International Journal of Business and Management Studies</p>
                        </div>
                        <div>
                            <Image src={Publication} alt="Publications logo" />
                        </div>
                    </div>
                </div>
                <div className='mt-6 border rounded-lg py-4 px-5 border-[#1952BB] border-opacity-20'>
                    <div className='flex justify-between items-center'>
                        <div className='flex flex-col gap-2.5'>
                            <div className='flex items-center gap-4'>
                                <span className='font-medium text-lg text-graySix'>Marine Biology, Climate Change</span>
                                <span className='font-medium text-xs text-primaryColor bg-primaryOne rounded-[16px] py-[2px] px-2'>Feb 12, 2023</span>
                            </div>
                            <p className='font-normal text-xs leading-5 tracking-[0.25px] text-graySix'>International Journal of Business and Management Studies</p>
                        </div>
                        <div>
                            <Image src={Publication} alt="Publications logo" />
                        </div>
                    </div>
                </div>
            </div>
            {/* <Table>
                <TableHeader>
                    <TableRow>
                        {tableHead.map((th, index) => (
                            <TableHead key={index} className='bg-white px-6 py-3.5 font-bold text-xs tracking-[0.4px] text-grayFive'>{th}</TableHead>
                        ))}
                    </TableRow>
                </TableHeader>
                <TableBody>
                    {data.map((item, index) => (
                        <TableRow className='hover:bg-primaryFour' key={index}>
                            <TableCell 
                                className="px-6 py-3.5 font-normal text-xs tracking-[0.4px] text-graySix"
                            >
                                {item.name}
                            </TableCell>
                            <TableCell 
                                className='px-6 py-3.5 flex items-center gap-2.5'
                            >
                                 {item.files && (
                                    <div className="flex flex-wrap gap-2 mt-1">
                                    {item.files.map((file, i) => (
                                        <span
                                        key={i}
                                        className="bg-primaryOne text-[#144296] px-2 py-1 rounded-[50px] text-xs flex items-center gap-1"
                                        >
                                        {file} <button onClick={() => console.log(`Remove ${file}`)}><X_icon className='text-[#144296]' /></button>
                                        </span>
                                    ))}
                                    </div>
                                )}
                            </TableCell>
                            <TableCell 
                                className="px-6 py-3.5 font-normal text-xs tracking-[0.4px] text-graySix"
                            >
                                <div className="flex justify-left gap-2">
                                    <Button onClick={() => handleUpload(index)} size="sm" >
                                        <Edit />
                                    </Button>
                                    <Button onClick={() => handleEdit(index)} size="sm">
                                        <Download />
                                    </Button>
                                    <Button onClick={() => handleUpload(index)} size="sm">
                                        <Upload className='text-primaryColor' />
                                    </Button>
                                    <Button onClick={() => handleView(index)} size="sm">
                                        <Visibility className="text-primaryColor" />
                                    </Button>
                                    </div>
                            </TableCell>
                        </TableRow>
                    ))}
                </TableBody>
            </Table> */}
        </SectionLayout>
    )
}

export default Documents