'use client';

import React from 'react';
import { useSelector } from 'react-redux';
import { RootState } from '@/lib/redux/store';
import Heading from './Heading';
import { studentDetails } from '@/common';
import {
    Avatar,
    AvatarFallback,
    AvatarImage,
  } from '@/components/ui/avatar';

const StudentProfile = () => {
    // Get student data from Redux store
    const currentStudent = useSelector((state: RootState) => state.student.currentStudent);

    const getStatusAndEmailValue = (value: string) => {
        switch (value) {
            case 'Email':
                return 'underline text-primaryColor';
            case 'Status':
                return 'text-graySix';
            default:
                return '';
        }
    };

    // Create dynamic student details from Redux data
    const dynamicStudentDetails = currentStudent ? [
        { label: 'Student ID', value: currentStudent.studentId?.toString() || 'N/A' },
        { label: 'Email', value: currentStudent.email || 'N/A' },
        { label: 'Phone', value: currentStudent.phone || 'N/A' },
        { label: 'Status', value: 'Active' }, // You can add status to StudentData interface if needed
    ] : studentDetails; // Fallback to static data if no current student

    // Get student name
    const studentName = currentStudent
        ? `${currentStudent.firstName || ''} ${currentStudent.lastName || ''}`.trim() || 'Student'
        : 'Faysal Ahmed';

    // Get initials for avatar fallback
    const getInitials = (name: string) => {
        return name.split(' ').map(n => n[0]).join('').toUpperCase().slice(0, 2);
    };

    return (
        <div className='flex gap-4 p-6 bg-white rounded-[20px] drop-shadow-[0_1px_2px_rgba(0, 0, 0, 0.05)]'>
            <div className='w-12 h-12 rounded-full overflow-hidden'>
                <Avatar>
                    <AvatarImage src='https://github.com/shadcn.png' alt={studentName} />
                    <AvatarFallback>{getInitials(studentName)}</AvatarFallback>
                </Avatar>
            </div>
            <div className='flex flex-col gap-3.5'>
                <div>
                    <Heading level='h2'>
                        {studentName}
                    </Heading>
                </div>
                <div className='flex space-x-32'>
                    {dynamicStudentDetails.map((student, index) => (
                        <div
                            key={index}
                            className='space-y-1.5 text-sm leading-4 text-grayFour'
                        >
                            <p className='font-normal'>{student.label}</p>
                            <p className={`font-semibold ${getStatusAndEmailValue(student.label)}`}>{student.value}</p>
                        </div>
                    ))}
                </div>
            </div>
        </div>
    )
}

export default StudentProfile