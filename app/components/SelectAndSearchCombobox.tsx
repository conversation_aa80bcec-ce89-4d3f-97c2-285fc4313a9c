import React from 'react';
import { cn } from '@/lib/utils';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { Check, ChevronsUpDown } from 'lucide-react';
import { SelectAndSearchComboboxProps } from '@/types';
import {
    Command,
    CommandEmpty,
    CommandGroup,
    CommandInput,
    CommandItem,
    CommandList,
  } from '@/components/ui/command';
import {
    Popover,
    PopoverContent,
    PopoverTrigger,
} from '@/components/ui/popover';


const SelectAndSearchCombobox:React.FC<SelectAndSearchComboboxProps> = ({
    selectLists,
    options,
    type,
    label,
    placeholder = "Select an option...",
    selectedValue,
    onChange,
    className,
    buttonClassName,
    width = "w-[200px]",
    disabled = false,
}) => {
    const [open, setOpen] = React.useState(false)
    const [value, setValue] = React.useState(selectedValue || "")

    React.useEffect(() => {
        setValue(selectedValue || "")
    }, [selectedValue])

    const handleSelect = (currentValue: string) => {
        const newValue = currentValue === value ? "" : currentValue
        setValue(newValue)
        onChange?.(newValue)
        setOpen(false)
    }
    
    const selectedLabel = options?.find((opt) => opt.value === value)?.label
    
    return (
        <>
        <div className='flex flex-col'>
            <Label 
                htmlFor={type} 
                className='font-medium text-sm text-grayFive leading-5 mb-1.5'
            >
                {label}
            </Label>

            <Popover open={open} onOpenChange={v => !disabled && setOpen(v)}>
                <PopoverTrigger asChild>
                    <Button
                        variant='outline'
                        role='combobox'
                        aria-expanded={open}
                        className={`${buttonClassName}font-medium text-base leading-6 text-grayFive w-full justify-between border border-tertiary border-opacity-20 rounded-lg py-2.5 px-3.5`}
                        disabled={disabled}
                    >
                        {selectedLabel || placeholder}
                        <ChevronsUpDown className=' ml-2 h-4 w-4 shrink-0 opacity-50' />
                    </Button>
                </PopoverTrigger>
                <PopoverContent className={`${className} w-[--radix-popover-trigger-width] max-h-[--radix-popover-content-available-height] p-0`}>
                    <Command>
                    <CommandInput placeholder={placeholder} />
                    <CommandList>
                        <CommandEmpty>No {label} found.</CommandEmpty>
                        <CommandGroup>
                        {options?.map((option, index) => (
                            <CommandItem
                                className='justify-between py-2'
                                key={index}
                                value={option.value}
                                onSelect={() => handleSelect(option.value)}
                            >
                                {option.label}
                            <Check
                                className={cn(
                                'mr-2 h-5 w-5 text-tertiary',
                                value === option.value ? "opacity-100" : "opacity-0"
                                )}
                            />
                            </CommandItem>
                        ))}
                        </CommandGroup>
                    </CommandList>
                    </Command>
                </PopoverContent>
            </Popover>
        </div>
        </>
    )
}

export default SelectAndSearchCombobox
