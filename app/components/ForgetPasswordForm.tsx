'use client';

import { z } from 'zod';
import Link from 'next/link';
import { useForm } from 'react-hook-form';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { zodResolver } from '@hookform/resolvers/zod';
import {
    Form,
    FormControl,
    FormField,
    FormItem,
    FormLabel,
    FormMessage,
} from '@/components/ui/form';

const formSchema = z.object({
    username: z.string().min(5, {
        message: 'Username must be at least 5 characters.',
    }),
    email: z.string().email({
        message: 'Please enter a valid email address.',
    }),
});

const ForgetPasswordForm = () => {
    const form = useForm<z.infer<typeof formSchema>>({
        resolver: zodResolver(formSchema),
        defaultValues: {
            email: '',
            username: '',
        },
    });

    function onSubmit(values: z.infer<typeof formSchema>) {
        // console.log(values);
        return values;
    }

    return (
        <div>
            <Form {...form}>
                <form
                    onSubmit={form.handleSubmit(onSubmit)}
                    className='space-y-5'
                >
                    <FormField
                        control={form.control}
                        name='email'
                        render={({ field }) => (
                            <FormItem className='space-y-1.5'>
                                <FormLabel className='text-sm font-medium text-graySix'>
                                    Email
                                </FormLabel>
                                <FormControl>
                                    <Input
                                        className='flex w-full rounded-[50px] outline-none focus:border-opacity-60 border focus-within:outline-none focus-within:ring-1 focus-within:ring-opacity-60 border-tertiary border-opacity-20 py-2.5 px-4 placeholder:text-grayThree shadow-[0px_1px_2px_0px_rgba(16,24,40,0.05)]'
                                        placeholder='Enter your email'
                                        {...field}
                                    />
                                </FormControl>
                                <FormMessage />
                            </FormItem>
                        )}
                    />

                    <Button
                        type='submit'
                        className='w-full bg-primaryColor rounded-[50px] py-2.5 mt-6 mb-5 hover:bg-tertiary text-white'
                    >
                        Reset Password
                    </Button>
                </form>
            </Form>
            <div className='flex justify-center gap-1 mt-8'>
                <span className='text-sm font-normal text-grayFive'>
                    Back to 
                </span>
                <Link
                    className='text-sm font-semibold text-primaryColor'
                    href={'/login'}
                >
                    Log in
                </Link>
            </div>
        </div>
    );
};

export default ForgetPasswordForm;
